// 引入ConfigurableForm的数据处理工具
import {handleMultiple} from '../../../components/ConfigurableForm/utils/data-processor';
Page({
  data: {
    externalFilterState: {}, // 企业名单回显
    showMapMode: false, // 不显示地图模式
    dropDownMenuConfig: ['region', 'industry', 'filter'] // 下拉菜单配置
  },

  onLoad(options) {
    let heightParams = options?.str;
    let tempExternalFilterState = {
      filterParams: {}
    };

    // 说明是从高级搜索那边过来的
    if (heightParams) {
      heightParams = JSON.parse(decodeURIComponent(heightParams));
      // console.log('原始参数:', heightParams);
      tempExternalFilterState = this.convertToDropDownMenuFormat(heightParams);
    }
    // console.log('最终转换结果:', tempExternalFilterState);

    this.setData({
      externalFilterState: tempExternalFilterState
    });
  },

  /**
   * 将 sear-term 的参数转换为 DropDownMenu 需要的格式
   * @param {Object} heightParams - 从 sear-term 传来的原始参数
   * @returns {Object} DropDownMenu 需要的格式
   */
  convertToDropDownMenuFormat(heightParams) {
    const result = {
      regionDataList: [],
      industrial_list: null,
      classic_industry_code_list: null,
      filterParams: {}
    };

    // 处理地区数据 - area_code_list 本身就是数组，可以直接赋值
    if (
      heightParams.area_code_list &&
      Array.isArray(heightParams.area_code_list)
    ) {
      result.regionDataList = heightParams.area_code_list;
    }

    // 处理产业数据
    if (
      heightParams.industrial_list &&
      Array.isArray(heightParams.industrial_list)
    ) {
      const processedIndustries = handleMultiple(heightParams.industrial_list);
      if (processedIndustries.length > 0) {
        // 取第一个产业作为选中项（DropDownMenu 目前只支持单选产业）
        const firstIndustry = processedIndustries[0];
        const industryData = {
          code: firstIndustry.code,
          name: firstIndustry.name
        };

        // 根据 tabCode 或 type 字段判断产业类型
        const tabCode = firstIndustry.tabCode || firstIndustry.type;
        if (tabCode === 'classic' || firstIndustry.classic) {
          // 经典产业链
          result.classic_industry_code_list = industryData;
        } else {
          // 热点产业链或产业链图谱
          result.industrial_list = industryData;
        }
      }
    }

    // 处理更多筛选参数
    const filterParams = {...heightParams};

    // 只排除已经在 DropDownMenu 中单独处理的字段
    delete filterParams.area_code_list; // 地区在 DropDownMenu 中单独处理
    delete filterParams.industrial_list; // 产业在 DropDownMenu 中单独处理

    // industry_code_list 和 ent_name 需要保留在更多筛选中

    // 只保留有值的筛选参数
    Object.keys(filterParams).forEach(key => {
      const value = filterParams[key];
      if (
        value === null ||
        value === undefined ||
        (Array.isArray(value) && value.length === 0) ||
        (typeof value === 'string' && value.trim() === '')
      ) {
        delete filterParams[key];
      }
    });

    if (Object.keys(filterParams).length > 0) {
      result.filterParams = filterParams;
    }
    return result;
  }
});
