// 引入ConfigurableForm的数据处理工具
import {
	handleMultiple,
	handleData,
	getNameFromPop
} from '../../../components/ConfigurableForm/utils/data-processor';


const app = getApp();

Page({
	data: {
		externalFilterState: {}, // 企业名单回显
		showMapMode: false, // 不显示地图模式
		dropDownMenuConfig: ['region', 'industry', 'filter'] // 下拉菜单配置
	},

	onLoad(options) {
		let heightParams = options?.str;
		let tempExternalFilterState = {
			filterParams: {}
		};
		// 说明是从高级搜索那边过来的
		if (heightParams) {
			heightParams = JSON.parse(decodeURIComponent(heightParams));
			console.log('原始参数:', heightParams);
			console.log('地区：', handleMultiple(heightParams.area_code_list));

			return
			// tempExternalFilterState['filterParams'] = 
		}

		this.setData({
			externalFilterState: tempExternalFilterState
		});
	}
});