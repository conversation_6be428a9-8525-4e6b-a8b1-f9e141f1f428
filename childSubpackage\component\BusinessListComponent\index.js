// 企业列表组件
import {
	home
} from '../../../service/api';
import {
	getHeight
} from '../../../utils/height.js';
const app = getApp();
Component({
	properties: {
		// 下拉菜单配置（主要配置项）
		dropDownMenuConfig: {
			type: Array,
			value: ['region', 'industry', 'filter'], // 默认配置：地区、产业链、更多筛选
			observer() {
				this.updateDropDownConfig();
			}
		},

		// 外部筛选状态（用于回显外部的筛选条件）
		externalFilterState: {
			type: Object,
			value: null,
			observer(newVal) {
				if (newVal && this.data.initialized) {
					this.applyExternalFilterState(newVal);
				}
			}
		},
	},

	data: {
		company_num: 0,
		requestData: [],
		initialized: false, // 标记组件是否已初始化

		// 写死的参数
		requestFunction: home.portrait, // 请求函数
		requestParams: {
			industrial_list: []
		}, // 请求参数

		industrial_list: [], // 产业链列表

		// 联系方式
		showContact: false,
		// 地址
		showAddress: false,
		addmarkers: [],
		locationTxt: '',
		location: {
			lat: '',
			lon: ''
		},
		locationMap: {},
		activeEntId: '', // 当前点击企业的id
		isLogin: false,
		computedHeight: 600, // 列表容器高度
		vipVisible: false, // VIP弹窗显示状态
		masonryVipVisible: false, // 未购买弹窗

		// 滚动相关状态
		showBottomVipPopup: false, // 是否显示底部VIP弹窗
		bottomVipOpacity: 0, // 底部VIP弹窗透明度
		scrollThrottleTimer: null, // 滚动节流定时器

		// 动态下拉菜单配置
		computedDropDownMenuConfig: [],
		dropDownMenuTitle: [], // 根据配置自动生成

		// 存储外部筛选状态
		externalFilterState: null,

		// 是否有活跃的弹窗
		hasActivePopup: false
	},

	lifetimes: {
		attached() {
			this.setData({
				isLogin: app.isLogin()
			});
			// 先更新配置，再初始化数据
			this.updateDropDownConfig();
		},
		ready() {
			// 标记初始化完成
			this.setData({
				initialized: true
			});

			// 如果有外部筛选状态，先应用它
			if (this.properties.externalFilterState) {
				this.applyExternalFilterState(this.properties.externalFilterState);
			}

			// 手动触发组件加载数据
			this.selectComponent('#refresh-scroll').loadData();
			this.handleHeight();
		}
	},

	methods: {
		// 更新下拉菜单配置
		updateDropDownConfig() {
			const {
				dropDownMenuConfig
			} = this.properties;

			// 根据配置生成对应的标题
			const titleMap = {
				region: '全国',
				industry: '产业类型',
				filter: '更多筛选'
			};

			const finalTitles = dropDownMenuConfig.map(
				config => titleMap[config] || '更多筛选'
			);
			this.setData({
				computedDropDownMenuConfig: dropDownMenuConfig,
				dropDownMenuTitle: finalTitles
			});
		},

		// 处理组件数据变化
		onDataChange(e) {
			let {
				list,
				total,
				hasMore
			} = e.detail;

			// 修复：home.portrait API 返回的是 items 字段，不是 list
			if (!list && e.detail.items) {
				list = e.detail.items;
			}

			this.setData({
				requestData: list,
				company_num: total || 0
			});

			// 向父组件传递数据变化事件
			this.triggerEvent('datachange', {
				list,
				total: total || 0
			});
		},

		// 筛选回调
		onFlitter(e) {
			const {
				area_code_list,
				industrial_list,
				classic_industry_code_list,
				name1,
				name2,
				...otherParams
			} = e.detail;

			let {
				requestParams,
				dropDownMenuTitle
			} = this.data;

			// 更新标题
			dropDownMenuTitle[0] = name1 || '全国';
			dropDownMenuTitle[1] = name2 || '产业类型';

			// 更新请求参数
			requestParams = {
				...requestParams,
				area_code_list: area_code_list || [],
				industrial_list: industrial_list || [],
				classic_industry_code_list: classic_industry_code_list || [],
				...otherParams
			};

			this.setData({
					requestParams,
					dropDownMenuTitle
				},
				() => {
					// 触发组件重新加载数据
					this.selectComponent('#refresh-scroll').reload();
				}
			);
		},

		// 应用外部筛选状态（用于回显外部的筛选条件）
		applyExternalFilterState(externalState) {
			if (!externalState) return;

			const {
				regionData,
				regionDataList,
				industrial_list,
				filterParams,
				classic_industry_code_list
			} = externalState;
			let {
				requestParams,
				dropDownMenuTitle
			} = this.data;

			// 处理地区数据 - 支持多个地区
			if (regionDataList && regionDataList.length > 0) {
				// 多个地区的情况
				const areaCodeList = regionDataList
					.map(item => item.code)
					.filter(code => code && code !== 'All');
				requestParams.area_code_list = areaCodeList;

				// 使用地区名称显示逻辑
				// const regionName = getNameFromPop(regionDataList) || '全国';
				dropDownMenuTitle[0] = regionName;
			} else if (regionData && regionData.code && regionData.code !== 'All') {
				// 单个地区的情况（兼容性）
				dropDownMenuTitle[0] = regionData.name || '全国';
				requestParams.area_code_list = [regionData.code];
			}

			// 处理产业链数据
			if (industrial_list && industrial_list.code) {
				requestParams.industrial_list = [industrial_list.code];
				dropDownMenuTitle[1] = industrial_list.name || '产业类型';
			}

			// 处理经典产业链数据
			if (classic_industry_code_list && classic_industry_code_list.code) {
				requestParams.classic_industry_code_list = [
					classic_industry_code_list.code
				];
				dropDownMenuTitle[1] = classic_industry_code_list.name || '产业类型';
			}

			// 处理其他筛选参数
			if (filterParams) {
				const {
					name1,
					name2,
					...otherParams
				} = filterParams;
				requestParams = {
					...requestParams,
					...otherParams
				};
			}

			// 更新组件状态
			this.setData({
				requestParams,
				dropDownMenuTitle,
				externalFilterState: externalState
			});
		},

		// 动态获取蒙层高度
		handleHeight() {
			const that = this;
			// 普通模式的高度计算
			getHeight(that, '.business-list-component .page_head', data => {
				this.setData({
					computedHeight: data.screeHeight - data.res[0].height,
					isLogin: app.isLogin()
				});
			});
		},

		// VIP弹窗
		vipPop(val) {
			if (val?.type === 'close') {
				this.setData({
					vipVisible: false
				});
				return;
			}
			this.setData({
				vipVisible: val
			});
		},

		// 关闭联系方式弹窗
		onContactClose() {
			this.setData({
				showContact: false
			});
		},

		// 关闭地址弹窗
		onCloseAddress() {
			this.setData({
				showAddress: false
			});
		},

		// 滚动到底部
		onScrollToLower(e) {
			// 传递给父组件
			this.triggerEvent('scrolltolower', e.detail);
		},

		// 滚动事件
		onScroll(e) {
			// 传递给父组件
			this.triggerEvent('scroll', e.detail);
		},

		// 卡片操作
		onCardAction(e) {
			// 传递给父组件
			this.triggerEvent('cardaction', e.detail);
		},

		// 标题点击
		handleTit(e) {
			// 传递给父组件
			this.triggerEvent('titleclick', e.detail);
		},

		// 地图模式
		goMapMode() {
			// 传递给父组件
			this.triggerEvent('mapmode');
		},

		// 导航到地图
		goMap() {
			// 传递给父组件
			this.triggerEvent('gomap');
		}
	}
});